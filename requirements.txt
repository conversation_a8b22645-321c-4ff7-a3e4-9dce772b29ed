# CoAT (Chain-of-Action-Thought) Project Dependencies
# Android in the Zoo: Chain-of-Action-Thought for GUI Agents

# Core dependencies
yacs>=0.1.8                    # Configuration management
pandas>=1.3.0                  # Data manipulation and analysis
numpy>=1.21.0                  # Numerical computing
Pillow>=8.3.0                  # Image processing (PIL)
jax>=0.3.0                     # Machine learning framework
jaxlib>=0.3.0                  # JAX library backend
requests>=2.25.0               # HTTP library
tqdm>=4.62.0                   # Progress bars
imagesize>=1.2.0               # Get image dimensions
python-Levenshtein>=0.12.0     # String similarity calculations
colorama>=0.4.4                # Colored terminal output

# API dependencies for different models
dashscope>=1.0.0               # Alibaba Cloud Qwen-VL API
openai>=1.0.0                  # OpenAI GPT-4V API (optional)
google-generativeai>=0.3.0     # Google Gemini API (optional)

# Additional utilities
PyYAML>=5.4.0                  # YAML configuration files
argparse                       # Command line argument parsing (built-in)
json                           # JSON handling (built-in)
os                             # Operating system interface (built-in)
time                           # Time-related functions (built-in)
random                         # Random number generation (built-in)
threading                      # Threading support (built-in)
traceback                      # Exception traceback (built-in)
enum                           # Enumeration support (built-in)
io                             # I/O operations (built-in)
base64                         # Base64 encoding (built-in)
tempfile                       # Temporary file operations (built-in)
re                             # Regular expressions (built-in)
collections                    # Specialized container datatypes (built-in)
functools                      # Higher-order functions (built-in)
typing                         # Type hints (built-in)
abc                            # Abstract base classes (built-in)
http                           # HTTP modules (built-in)

# Development and testing (optional)
pytest>=6.0.0                  # Testing framework
pytest-cov>=2.12.0             # Coverage plugin for pytest
